<script setup lang="ts">
import { onHide, onLaunch, onShow } from '@dcloudio/uni-app'
import { navigateToInterceptor } from '@/router/interceptor'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import { getTenantConfig, getTenantConfigByAppId } from '@/api/config'
import { useTenantStore, useEnumStore } from '@/store'

onLaunch(async (options) => {
  console.log('App Launch', options)
  if (options.query.tenantCode) {
    console.log('tenantCode', options.query.tenantCode)
    const res: any = await getTenantConfig({ tenantCode: options.query.tenantCode })
    if (res.code === 200) {
      console.log('tenantConfig', res.data)
      useTenantStore().setTenantInfo(res.data.tenantInfo)
    }
  } else {
    const res: any = await getTenantConfigByAppId({ appId: import.meta.env.VITE_WX_APPID, type: 2 })
    if (res.code === 200 && res.data) {
      useTenantStore().setTenantInfo(res.data.tenantInfo)
    }
  }
  // 获取枚举数据
  useEnumStore().getEnumData()
})
onShow((options) => {
  console.log('App Show', options)
  // 处理直接进入页面路由的情况：如h5直接输入路由、微信小程序分享后进入等
  // https://github.com/unibest-tech/unibest/issues/192
  if (options?.path) {
    navigateToInterceptor.invoke({ url: `/${options.path}`, query: options.query })
  }
  else {
    navigateToInterceptor.invoke({ url: '/' })
  }
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
</style>
