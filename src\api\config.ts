import { http } from "@/http/http";

// 获取所有枚举数据
export function getAllEnums(params: any) {
    return http.get('/tur/common/getAllEnums', params)
}
// 获取景区列表
export function getScenicList(params: any) {
    return http.get('/tur/scenic/list', params)
}

// 获取oss配置
export function getOssConfig() {
    return http.get('/tur/common/getStsToken')
}

// 租户编码获取租户配置
export function getTenantConfig(prams: any) {
    return http.get('/tur/tenant/configByCode', prams)
}

// 通过appid获取租户配置
export function getTenantConfigByAppId(prams: any) {
    return http.get('/tur/tenant/configByAppid', prams)
}

// 获取单个配置
export function getConfigByScene(params: any) {
    return http.get('/tur/config/infoByScene', params)
}