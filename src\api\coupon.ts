import { http } from '@/http/http'

export function getUserCouponList(params: any) {
    return http.get('/tur/userCoupon/list', params)
}

/**
 * 领券中心
 * @param params 
 * @returns 
 */
export function getCouponList(params: any) {
    return http.get('/tur/coupon/list', params)
}

export function getCouponDetail(params: any) {
    return http.get('/tur/coupon/info', params)
}

export function receiveCoupon(id: any) {
    return http.post('/tur/coupon/receive', { id })
}