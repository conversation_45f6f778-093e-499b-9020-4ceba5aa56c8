import { http } from '@/http/http'

// 获取卡信息
export function getPrepaidCardInfo(params: any) {
    return http.get('/tur/prepaidCard/info', params)
}
// 获取消费记录
export function getPrepaidCardLog(params: any) {
    return http.get('/tur/prepaidCard/getCardlog', params)
}
// 开卡
export function openPrepaidCard(data: any) {
    return http.post('/tur/prepaidCard/open', data)
}

// 充值
export function rechargePrepaidCard(data: any) {
    return http.post('/tur/prepaidCard/recharge', data)
}

// 充值记录
export function getPrepaidCardRechargeLog(params: any) {
    return http.get('/tur/prepaidCard/getRechargeOrder', params)
}

// 充值结果查询
export function getPrepaidCardRechargeResult(params: any) {
    return http.get('/tur/prepaidCard/payQuery', params)
}

// 修改支付密码
export function changePrepaidCardPayPassword(data: any) {
    return http.post('/tur/prepaidCard/changePayPassword', data)
}