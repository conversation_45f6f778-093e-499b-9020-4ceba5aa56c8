import { http } from '@/http/http'

export function getTourists(params: any) {
    return http.get('/tur/tourist/list', params)
}

export function createTourist(data: any) {
    return http.post('/tur/tourist/create', data)
}

export function updateTourist(data: any) {
    return http.post('/tur/tourist/update', data)
}
export function deleteTourist(data: any) {
    return http.post('/tur/tourist/delete', data)
}

export function setDefaultTourist(id: string) {
    return http.post('/tur/tourist/setDefault', { id })
}