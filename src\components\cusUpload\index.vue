<script lang="ts" setup>
import type { UploadMethod, UploadFile } from 'wot-design-uni/components/wd-upload/types'
import dayjs from 'dayjs'
import crypto from 'crypto-js'
import { Base64 } from 'js-base64'
import { getCacheDataByKey } from '@/hooks/useCache'

const props = defineProps({
    modelValue: { type: Array, default: () => [] },
    limit: { type: Number, default: 9 },
    showLimitNum: { type: Boolean, default: false },
    accept: { type: String, default: 'image' },
    multiple: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    fileTypeTag: { type: String, default: '' },
})
const emit = defineEmits(['update:modelValue', 'success', 'error'])

const fileList = ref<UploadFile[]>([])

watch(() => props.modelValue, (val) => {
    fileList.value = val || []
}, { deep: true })
/**
 * 更新文件名
 */
function updateFileName(filePath: string) {
    const startIndex = filePath.lastIndexOf('.')
    const suffix = filePath.substring(startIndex, filePath.length).toLowerCase()
    // #ifdef MP
    const fileType = filePath.split(".").pop();
    // #endif

    const date = dayjs().format('YYYYMMDD')
    const time = dayjs().valueOf()
    return `${date}/${randomString()}${time}.${fileType}`
}


/**
 * 获取OSS配置信息(带缓存)
 */
async function getOSSConfig() {
    return await getCacheDataByKey('ossConfig')
}

// 签名
function computeSignature(accessKeySecret, canonicalString) {
    return crypto.enc.Base64.stringify(
        crypto.HmacSHA1(canonicalString, accessKeySecret)
    );
}
// 生成随机字符串默认8位
function randomString(len = 8) {
    const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
    let result = ''
    for (let i = 0; i < len; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
}
const customUpload: UploadMethod = async (file, formData, options) => {
    // 获取文件名
    const fileKey = updateFileName(file.url)
    // 获取OSS配置
    const ossConfig: any = await getOSSConfig()
    console.log('OSS配置:', ossConfig)
    // 构建OSS文件路径
    let ossKey = `${ossConfig.prefixPath}${fileKey}`
    if (props.fileTypeTag && props.fileTypeTag !== '') {
        ossKey = `${ossConfig.prefixPath}${props.fileTypeTag}/${fileKey}`
    }

    // 生成文件URL
    const fileUrl = `${ossConfig.protocol}://${ossConfig.host}/${ossKey}`

    const date = new Date();
    date.setHours(date.getHours() + 1);
    const policyText = {
        expiration: date.toISOString(), // 设置policy过期时间。
        conditions: [["content-length-range", 0, 1024 * 1024 * 1024]],
    };
    const policy = Base64.encode(JSON.stringify(policyText)); // policy必须为base64的string。
    const signature = computeSignature(ossConfig.accessKeySecret, policy);
    const uploadTask = uni.uploadFile({
        url: `https://${ossConfig.bucket}.${ossConfig.region}.aliyuncs.com`,
        name: 'file',
        formData: {
            key: ossKey,
            OSSAccessKeyId: ossConfig.accessKeyId,
            signature, // 使用后端返回的signature
            policy,
            'x-oss-security-token': ossConfig.securityToken, // STS临时令牌需要
            success_action_status: 200
        },
        filePath: file.url,
        success(res) {
            if (res.statusCode === 200) { // 成功状态码为200
                res.data = fileUrl; // 自定义属性，用于保存文件URL
                // 设置上传成功
                options.onSuccess(res, file, formData)
            } else {
                // 设置上传失败
                options.onError({ ...res, errMsg: res.errMsg || '' }, file, formData)
            }
        },
        fail(err) {
            // 设置上传失败
            options.onError(err, file, formData)
        }
    })
    // 设置当前文件加载的百分比
    uploadTask.onProgressUpdate((res) => {
        console.log('onProgressUpdate', res)
        options.onProgress(res, file)
    })
}

const handleFileChange = ({ fileList }) => {
    console.log('handleFileChange', fileList)
}
const handleSuccess = ({ file, fileList, formData }) => {
    console.log('handleSuccess', file, fileList, formData)
    emit('update:modelValue', fileList)
    emit('success', file)
}
const handleFail = ({ error, file, formData }) => {
    console.log('handleFail', error, file, formData)
}
const handleRemove = ({ file }) => {
    console.log('handleRemove', file)
}
const handleProgress = ({ response, file }) => {
    console.log('handleProgress', response, file)
}
</script>
<template>
    <wd-upload :file-list="fileList" @change="handleFileChange" @success="handleSuccess" @fail="handleFail"
        @remove="handleRemove" @progress="handleProgress" :multiple="multiple" auto-upload :limit="limit"
        :showLimitNum="showLimitNum" :upload-method="customUpload" :disabled="disabled"></wd-upload>
</template>