<template>
  <view class="show-more">
    <view ref="contentRef" class="content" :class="{
      'content-collapsed': !isExpanded && shouldClamp,
      'content-expanded': isExpanded || !shouldClamp
    }" :style="contentStyle">
      <slot></slot>
      <!-- 渐变遮罩 -->
      <view v-if="!isExpanded && shouldClamp && showShadow" class="gradient-mask"></view>
    </view>
    <view v-if="showToggle" class="text-primary text-center" @click="toggleExpanded">
      <text class="toggle-text">{{ isExpanded ? hideText : showText }}</text>
      <wd-icon :name="isExpanded ? 'arrow-up' : 'arrow-down'" size="14" color="#666"></wd-icon>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, computed, getCurrentInstance } from 'vue'

interface Props {
  // 最大显示行数，超出后可折叠
  maxLines?: number
  // 展开时显示的文本
  showText?: string
  // 收起时显示的文本
  hideText?: string
  // 行高，用于更精确的计算（rpx）
  lineHeight?: number
  // 是否显示渐变遮罩
  showGradient?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxLines: 3,
  showText: '显示更多',
  hideText: '收起',
  lineHeight: 40, // 默认行高 40rpx
  showGradient: true
})

const isExpanded = ref(false)
const showToggle = ref(false)
const showShadow = ref(false)
const shouldClamp = ref(false)
const contentRef = ref<any>(null)
const contentHeight = ref(0)
const maxAllowedHeight = ref(0)

// 计算样式
const contentStyle = computed(() => {
  const style: any = {}

  if (!isExpanded.value && shouldClamp.value) {
    // 使用固定高度限制
    style.maxHeight = `${maxAllowedHeight.value}rpx`
    style.overflow = 'hidden'
  }

  return style
})

// 切换展开/收起状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 检查内容是否超出指定行数
const checkContentOverflow = async () => {
  if (!props.maxLines) {
    showToggle.value = false
    shouldClamp.value = false
    return
  }

  // 计算最大允许高度
  maxAllowedHeight.value = props.maxLines * props.lineHeight
  // console.log('showMore组件: 开始检查内容溢出', {
  //   maxLines: props.maxLines,
  //   lineHeight: props.lineHeight,
  //   maxAllowedHeight: maxAllowedHeight.value
  // })

  await nextTick()

  // 延迟执行，确保DOM渲染完成
  setTimeout(() => {
    measureContent()
  }, 100)

  // 添加备用检查，防止第一次检查失败
  setTimeout(() => {
    if (!showToggle.value && !shouldClamp.value) {
      // console.log('showMore组件: 第一次检查失败，进行备用检查')
      measureContent()
    }
  }, 300)

  // 最后一次检查
  setTimeout(() => {
    if (!showToggle.value && !shouldClamp.value) {
      // console.log('showMore组件: 备用检查也失败，进行最后检查')
      measureContent()
    }
  }, 800)
}

// 测量内容高度
const measureContent = () => {
  const instance = getCurrentInstance()
  let query: UniApp.SelectorQuery

  // 检查 instance 是否存在，避免 $scope 错误
  if (instance) {
    query = uni.createSelectorQuery().in(instance)
  } else {
    console.warn('showMore组件: getCurrentInstance() 返回 null，使用全局查询')
    // 使用全局查询作为备用方案
    query = uni.createSelectorQuery()
  }

  // 尝试多个选择器
  const selectors = ['.show-more .content', '.content', '.show-more']
  let currentSelectorIndex = 0

  const tryMeasure = () => {
    if (currentSelectorIndex >= selectors.length) {
      console.warn('showMore组件: 所有选择器都无法获取内容高度')
      // 如果所有选择器都失败，强制显示展开按钮作为备用方案
      shouldClamp.value = true
      showToggle.value = true
      showShadow.value = props.showGradient
      return
    }

    const selector = selectors[currentSelectorIndex]
    query.select(selector).boundingClientRect((res: any) => {
      // console.log(`showMore组件测量结果(${selector}):`, res)
      // console.log('最大允许高度:', maxAllowedHeight.value)

      if (res && res.height && res.height > 0) {
        contentHeight.value = res.height
        // 将 rpx 转换为 px 进行比较（小程序中 res.height 是 px 单位）
        const maxHeightPx = uni.upx2px(maxAllowedHeight.value)
        const isOverflow = res.height > maxHeightPx

        // console.log('内容高度(px):', res.height, '最大高度(px):', maxHeightPx, '是否溢出:', isOverflow)

        shouldClamp.value = isOverflow
        showToggle.value = isOverflow
        showShadow.value = isOverflow && props.showGradient
      } else {
        console.warn(`showMore组件: 选择器 ${selector} 无法获取内容高度，尝试下一个`)
        currentSelectorIndex++
        setTimeout(tryMeasure, 50)
      }
    }).exec()
  }

  tryMeasure()
}

// 监听内容变化，重新计算是否需要显示展开按钮
onMounted(() => {
  checkContentOverflow()

  // 延迟检查，确保内容渲染完成
  setTimeout(() => {
    checkContentOverflow()
  }, 200)

  // 再延迟一次确保渲染完成
  setTimeout(() => {
    checkContentOverflow()
  }, 500)
})

// 暴露方法给父组件
defineExpose({
  checkContentOverflow,
  toggleExpanded
})
</script>

<style scoped lang="scss">
.show-more {
  .content {
    position: relative;
    transition: max-height 0.3s ease-in-out;

    &.content-collapsed {
      overflow: hidden;
    }

    &.content-expanded {
      max-height: none !important;
    }
  }

  .gradient-mask {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60rpx;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
    pointer-events: none;
  }
}
</style>