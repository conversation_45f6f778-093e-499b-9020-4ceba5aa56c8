import { http } from '@/http/http'
import dayjs from 'dayjs'
import crypto from 'crypto-js'
import { Base64 } from 'js-base64'

interface OSSConfig {
  accessKeyId: string
  accessKeySecret: string
  stsToken: string
  region: string
  bucket: string
  expiration: string
  securityToken: string
  prefixPath: string
  protocol: string
  host: string
  policy: string // 添加policy字段
  signature: string // 添加signature字段
}

interface UploadToOSSParams {
  filePath: string
  fileTypeTag: string
  fileName?: string
  onProgress?: (progress: number) => void
}

// 生成随机字符串默认8位
function randomString(len = 8) {
  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  let result = ''
  for (let i = 0; i < len; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 更新文件名
 */
function updateFileName(filePath: string) {
  const startIndex = filePath.lastIndexOf('.')
  const suffix = filePath.substring(startIndex, filePath.length).toLowerCase()
  // #ifdef MP
  const fileType = filePath.split(".").pop();
  // #endif

  const date = dayjs().format('YYYYMMDD')
  const time = dayjs().valueOf()
  return `${date}/${randomString()}${time}.${fileType}`
}

/**
 * 获取OSS配置信息
 */
async function getOSSConfig(): Promise<OSSConfig> {
  const res = await http.get<OSSConfig>('/tur/common/getStsToken')
  return res.data
}

/**
 * 上传文件到阿里云OSS（适用于uniapp小程序环境）
 * @param options 上传选项
 * @returns Promise<string> 返回文件URL
 */
export async function uploadToOSS(options: UploadToOSSParams): Promise<string> {
  const { filePath, fileTypeTag, fileName, onProgress } = options

  // 获取文件名
  const fileKey = updateFileName(filePath)
  console.log('文件路径:', filePath)

  // 获取OSS配置
  const ossConfig = await getOSSConfig()
  console.log('OSS配置:', ossConfig)

  // 构建OSS文件路径
  let ossKey = `${ossConfig.prefixPath}${fileKey}`
  if (fileTypeTag && fileTypeTag !== '') {
    ossKey = `${ossConfig.prefixPath}${fileTypeTag}/${fileKey}`
  }

  // 生成文件URL
  const fileUrl = `${ossConfig.protocol}://${ossConfig.host}/${ossKey}`

  const date = new Date();
  date.setHours(date.getHours() + 1);
  const policyText = {
    expiration: date.toISOString(), // 设置policy过期时间。
    conditions: [["content-length-range", 0, 1024 * 1024 * 1024]],
  };
  const policy = Base64.encode(JSON.stringify(policyText)); // policy必须为base64的string。
  const signature = computeSignature(ossConfig.accessKeySecret, policy);

  // 使用uni.uploadFile上传到OSS
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: `https://${ossConfig.bucket}.${ossConfig.region}.aliyuncs.com`,
      filePath,
      name: 'file',
      formData: {
        key: ossKey,
        OSSAccessKeyId: ossConfig.accessKeyId,
        signature, // 使用后端返回的signature
        policy,
        'x-oss-security-token': ossConfig.securityToken, // STS临时令牌需要
        success_action_status: 200
      },
      success: (uploadFileRes) => {
        if (uploadFileRes.statusCode === 200) {
          console.log('上传成功，文件URL:', fileUrl)
          resolve(fileUrl)
        } else {
          reject(new Error(`上传失败，状态码：${uploadFileRes.statusCode}`))
        }
      },
      fail: (err) => {
        console.error('上传失败:', err)
        reject(err)
      },
      complete: () => {
        // 上传完成
      }
    })
  })
}

// 签名
function computeSignature(accessKeySecret, canonicalString) {
  return crypto.enc.Base64.stringify(
    crypto.HmacSHA1(canonicalString, accessKeySecret)
  );
}

/**
 * 简化版自定义上传函数，适用于uniapp小程序环境
 */
export async function customOssUpload(options: UploadToOSSParams): Promise<string> {
  return uploadToOSS(options)
}
