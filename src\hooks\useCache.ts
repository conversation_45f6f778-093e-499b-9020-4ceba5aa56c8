import { getOssConfig } from '@/api/config'

interface ICacheOptions {
  /** 缓存有效期，单位毫秒，默认50分钟 */
  expire?: number
}

interface ICacheReturn<T> {
  /** 获取缓存数据，如果缓存过期或不存在则重新请求 */
  getData: () => Promise<T>
  /** 清除缓存 */
  clearCache: () => void
}

// 接口缓存配置列表
interface IApiConfig {
  key: string
  func: () => Promise<any>
  expire?: number
}

// 预定义的接口缓存配置
const API_CACHE_CONFIGS: IApiConfig[] = [
  {
    key: 'ossConfig',
    func: getOssConfig,
    expire: 50 * 60 * 1000 // 50分钟
  }
]

/**
 * useCache是一个用于处理接口数据缓存的钩子函数
 * @param key 缓存的唯一键值
 * @param func 获取数据的异步函数
 * @param options 缓存选项
 * @returns 返回包含获取数据和清除缓存方法的对象
 */
export default function useCache<T>(
  key: string,
  func: () => Promise<T>,
  options: ICacheOptions = { expire: 50 * 60 * 1000 } // 默认50分钟
): ICacheReturn<T> {
  const cacheKey = `cache_${key}`
  const cacheTimeKey = `cache_time_${key}`

  const getData = async (): Promise<T> => {
    // 检查是否有缓存且未过期
    const cachedData = uni.getStorageSync(cacheKey)
    const cacheTime = uni.getStorageSync(cacheTimeKey)
    const now = Date.now()

    if (cachedData && cacheTime && (now - cacheTime) < (options.expire || 50 * 60 * 1000)) {
      console.log(`使用缓存数据: ${key}`)
      return JSON.parse(cachedData)
    }

    // 无缓存或缓存过期，重新请求
    const res: any = await func()

    // 存储到缓存中
    uni.setStorageSync(cacheKey, JSON.stringify(res.data))
    uni.setStorageSync(cacheTimeKey, now)

    console.log(`重新请求数据并缓存: ${key}`)
    return res.data
  }

  const clearCache = (): void => {
    uni.removeStorageSync(cacheKey)
    uni.removeStorageSync(cacheTimeKey)
  }

  return { getData, clearCache }
}

/**
 * 注册接口缓存配置
 * @param configs 接口缓存配置数组
 */
export function registerApiCacheConfigs(configs: IApiConfig[]) {
  API_CACHE_CONFIGS.push(...configs)
}

/**
 * 通过key获取缓存数据
 * @param key 配置的键值
 * @returns 返回缓存数据
 */
export async function getCacheDataByKey<T>(key: string): Promise<T> {
  const config = API_CACHE_CONFIGS.find(item => item.key === key)

  if (!config) {
    throw new Error(`未找到key为"${key}"的缓存配置`)
  }

  const cache = useCache<T>(key, config.func, { expire: config.expire })
  return await cache.getData()
}

/**
 * 通过key清除缓存
 * @param key 配置的键值
 */
export function clearCacheByKey(key: string): void {
  const cacheKey = `cache_${key}`
  const cacheTimeKey = `cache_time_${key}`
  uni.removeStorageSync(cacheKey)
  uni.removeStorageSync(cacheTimeKey)
}