// 获取屏幕边界到安全区域距离
let safeAreaInsets: any = {}
let systemInfo: any = {}

export const useSafeAreaInsets = () => {
    // #ifdef MP-WEIXIN
    // 微信小程序使用新的API
    systemInfo = uni.getWindowInfo()
    safeAreaInsets = systemInfo.safeArea
        ? {
            top: systemInfo.safeArea.top,
            right: systemInfo.windowWidth - systemInfo.safeArea.right,
            bottom: systemInfo.windowHeight - systemInfo.safeArea.bottom,
            left: systemInfo.safeArea.left,
        }
        : null
    // #endif

    // #ifndef MP-WEIXIN
    // 其他平台继续使用uni API
    systemInfo = uni.getSystemInfoSync()
    safeAreaInsets = systemInfo.safeAreaInsets
    // #endif
    return safeAreaInsets
}
