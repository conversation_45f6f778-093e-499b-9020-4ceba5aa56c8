<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarBackgroundColor": "#ffffff",
        "navigationBarTitleText": "投诉建议"
    }
}</route>

<script lang="ts" setup>
import myComplaint from './components/myComplaint.vue';
import addComplaint from './components/addComplaint.vue';
const tab = ref(0)

</script>

<template>
    <wd-tabs v-model="tab" :sticky="true">
        <wd-tab title="投诉建议">
            <view class="bg-[#f6f6f6]">
                <addComplaint></addComplaint>
            </view>
        </wd-tab>
        <wd-tab title="我的投诉">
            <view class="bg-[#f6f6f6]">
                <myComplaint></myComplaint>
            </view>
        </wd-tab>
    </wd-tabs>

</template>

<style lang="scss" scoped>
//</style>
