<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">{
  "style": {
    // 'custom' 表示开启自定义导航栏，默认 'default'
    "navigationStyle": "custom",
    "navigationBarTitleText": "首页"
  }
}</route>

<script lang="ts" setup>
import { useThemeStore } from '@/store'

defineOptions({
  name: 'Home',
})

const { theme,isDark, toggleTheme, setThemeVars } = useThemeStore()

const goPage = (url: string) => {
  uni.navigateTo({ url })
}

</script>

<template>
  <view class="bg-white px-4 pt-2 pt-safe">
    <view class="mt-10">
      <image src="/static/logo.svg" alt="" class="mx-auto block h-28 w-28" />
    </view>
    <view class="mt-4 text-center text-4xl text-[#d14328]">
      欢迎使用
    </view>
    <view class="mb-8 mt-2 text-center text-2xl">
      您的应用
    </view>

    <view class="m-auto mb-2 max-w-100 text-center text-4">
      这是一个基于 uni-app 的应用，您可以开始构建您的功能。
    </view>

    <view class="mt-4 text-center">
      <wd-button type="primary" class="ml-2" @click="setThemeVars({ colorTheme: '#37c2bc' })">
        设置主题变量
      </wd-button>
    </view>
    <view class="flex items-center mb-3">
      暗黑模式：{{ isDark }}
      <wd-switch v-model="isDark"></wd-switch>
    </view>
    <view>
      <wd-button type="primary" class="ml-2" @click="goPage('/pages/notice/index')"> 公告 </wd-button>
    </view>
    <view class="h-6" />
  </view>
</template>
