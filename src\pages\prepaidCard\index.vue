<route lang="jsonc">{
    "layout": "default",
    "style": {
        "navigationBarBackgroundColor": "#fff",
        "navigationBarTitleText": "一卡通"
    }
}</route>

<script lang="ts" setup>
import { getPrepaidCardInfo, rechargePrepaidCard, getPrepaidCardRechargeResult } from '@/api/prepaidCard';
import { getConfigByScene } from '@/api/config';
import { useTenantStore } from '@/store';
const tenantStore = useTenantStore()
console.log('tenantStore', tenantStore.tenantInfo)
onLoad(() => {
    getInfo()
    getConfig()
})
onUnmounted(() => {
    clearInterval(timer.value)
})
const prepaidCardInfo = ref<any>({});
const getInfo = async () => {
    const res = await getPrepaidCardInfo({});
    console.log(res);
    if (res.code === 200) {
        prepaidCardInfo.value = res.data;
    }
}
const rechargeConfig = ref<any>({});
const getConfig = async () => {
    const res = await getConfigByScene({ scene: 'prepaidCard', tenantId: tenantStore.tenantInfo.tenantId });
    if (res.code === 200) {
        rechargeConfig.value = res.data;
    }
    console.log(res);
}

// 充值
const selectAmount = ref<any>({});
const selectType = ref<any>(1);
const timer = ref(null)
const recharge = async (value: any, type: any) => {
    if (type == 2 && selectType.value == 2) return
    selectType.value = type;
    if (type === 1) {
        selectAmount.value = value;
    } else {
        selectAmount.value = {
            amount: undefined,
            giftAmount: 0
        }
    }
}
const handleRecharge = async () => {
    const res: any = await rechargePrepaidCard({
        amount: selectAmount.value.amount,
        giftAmount: selectAmount.value.giftAmount,
        cardNo: prepaidCardInfo.value.cardNo
    })
    if (res.code === 200) {
        console.log('selectAmount', res.data.payConfig)
        let orderInfo = {
            orderId: res.data.orderId,
            orderNo: res.data.orderNo
        }
        console.log('orderInfo', orderInfo)
        uni.requestPayment({
            provider: 'wxpay',
            orderInfo: JSON.stringify(orderInfo),
            timeStamp: res.data.payConfig.payTimeStamp,
            nonceStr: res.data.payConfig.paynonceStr,
            package: res.data.payConfig.payPackage,
            paySign: res.data.payConfig.paySign,
            signType: res.data.payConfig.paySignType,
            success: function (res) {
                console.log('success', res);
                if (res.errMsg === 'requestPayment:ok') {
                    uni.showLoading({
                        title: '获取支付结果中...',
                        mask: true
                    })
                    timer.value = setInterval(() => {
                        getRechargeResult(orderInfo)
                    }, 1500)
                }
            },
            fail: (fail) => {
                uni.hideLoading()
                console.log('fail ', fail)
            },
        })
    }
}
const maxNum = ref(0)
const getRechargeResult = async (orderInfo: { orderId: string, orderNo: string }) => {
    if (maxNum.value >= 40) {
        uni.hideLoading()
        uni.showToast({
            title: '获取支付结果失败',
            icon: 'error'
        })
        clearInterval(timer.value)
        return
    }
    maxNum.value++
    const res: any = await getPrepaidCardRechargeResult({
        orderId: orderInfo.orderId,
        orderNo: orderInfo.orderNo
    })
    if (res.code == 200 && res.data.orderStatus == 2) {
        uni.hideLoading()
        uni.showToast({
            title: '充值成功',
            icon: 'success'
        })
        getInfo()
        clearInterval(timer.value)
    } else if (res.data.orderStatus !== 1 && res.data.orderStatus !== 2) {
        uni.hideLoading()
        uni.showToast({
            title: '充值失败',
            icon: 'error'
        })
        clearInterval(timer.value)
    }
}
</script>

<template>
    <view class="p-3">
        <view class="p-2 rounded mb-4 text-white bg-primary">
            <view class="text-sm mb-3">卡内余额</view>
            <view class="text-2xl font-bold">{{ prepaidCardInfo.balance }}</view>
            <view class="text-right text-sm">No.{{ prepaidCardInfo.cardNo }}</view>
        </view>
        <view>
            <view class="mb-3">余额充值</view>
            <view class="grid grid-cols-3 gap-2">
                <view class="rounded px-2 py-3 flex flex-col justify-center items-center"
                    v-for="item in rechargeConfig.levelConfig" :key="item.amount" @click="recharge(item, 1)"
                    :class="selectAmount.amount == item.amount ? 'bg-primary text-white' : 'bg-white'">
                    <view>￥{{ item.amount }}</view>
                    <view class="text-xs" v-if="item.giftAmount > 0">赠送{{ item.giftAmount }}</view>
                </view>
                <view class="bg-white rounded px-2 py-3 flex justify-center items-center" @click="recharge(null, 2)"
                    v-if="rechargeConfig.weixinOpenStatus">
                    <view class="text-sm" v-if="selectType == 1">
                        自定义金额
                        <wd-icon name="edit-1" size="22px"></wd-icon>
                    </view>
                    <wd-input v-model="selectAmount.amount" :no-border="true" :focus="true" placeholder="请输入金额"
                        v-else></wd-input>
                </view>
            </view>
        </view>
        <view class="mb-4 mt-8 px-4">
            <wd-button type="primary" block @click="handleRecharge">充值</wd-button>
        </view>
        <view>
            <wd-cell-group :border="true">
                <wd-cell title="充值记录" is-link to="/pages/prepaidCard/rechargeLog">
                    <template #icon>
                        <wd-icon name="view-module" size="22px" custom-class="mr-1"></wd-icon>
                    </template>
                </wd-cell>
                <wd-cell title="消费记录" is-link to="/pages/prepaidCard/consumeLog">
                    <template #icon>
                        <wd-icon name="list" size="22px" custom-class="mr-1"></wd-icon>
                    </template>
                </wd-cell>
                <wd-cell title="修改支付密码" is-link to="/pages/prepaidCard/changePayPassword"
                    v-if="rechargeConfig.payUsePassword">
                    <template #icon>
                        <wd-icon name="lock-on" size="22px" custom-class="mr-1"></wd-icon>
                    </template>
                </wd-cell>
            </wd-cell-group>
        </view>
    </view>
</template>

<style lang="scss"></style>
