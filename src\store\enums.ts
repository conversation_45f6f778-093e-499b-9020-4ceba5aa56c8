import { getAllEnums } from '@/api/config'
import { defineStore } from 'pinia'
import { ref } from 'vue'

// 枚举数据
const enumState = {
    
}

export const useEnumStore = defineStore('enums', () => {
    const enumData = ref<any>({ ...enumState })
    // 设置枚举数据
    const setEnumData = (data: any) => {
        enumData.value = data
    }
    //删除枚举数据
    const removeEnumData = () => {
        enumData.value = { ...enumState }
        uni.removeStorageSync('enums')
    }
    // 获取枚举数据
    const getEnumData = async () => {
        const res = await getAllEnums({})
        setEnumData(res.data)
        return res
    }

    return {
        enumData,
        getEnumData,
        setEnumData,
        removeEnumData
    }
},
    {
        persist: true,
    },
)