import { defineStore } from 'pinia'
import { ref } from 'vue'

const tenantState = {
    tenantInfo: {},
}

export const useTenantStore = defineStore(
    'tenant',
    () => {
        // 获取租户信息
        const tenantInfo = ref<any>(tenantState.tenantInfo)

        const setTenantInfo = (val: any) => {
            tenantInfo.value = val
        }
        // 删除租户信息
        const removeTenantInfo = () => {
            tenantInfo.value = { ...tenantState.tenantInfo }
            uni.removeStorageSync('tenant')
        }
        return {
            tenantInfo,
            removeTenantInfo,
            setTenantInfo,
        }
    }, {
    persist: true
}
)