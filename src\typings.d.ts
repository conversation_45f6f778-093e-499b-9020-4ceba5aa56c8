// 全局要用的类型放到这里

declare global {
  interface IResData<T> {
    code: number
    message: string
    data: T
  }

  // uni.uploadFile文件上传参数
  interface IUniUploadFileOptions {
    file?: File
    files?: UniApp.UploadFileOptionFiles[]
    filePath?: string
    name?: string
    formData?: any
  }

  interface IUserInfo {
    nickname?: string
    avatar?: string
    /** 微信的 openid，非微信没有这个字段 */
    openid?: string
  }

  interface IUserToken {
    token: string
    refreshToken?: string
    refreshExpire?: number
  }
}

export {} // 防止模块污染
